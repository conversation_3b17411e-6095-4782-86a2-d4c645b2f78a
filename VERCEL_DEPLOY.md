# 🚀 Hướng dẫn Deploy lên Vercel

## ⚠️ Vấn đề hiện tại
AI không hoạt động trên Vercel vì:
1. **Model name sai**: Đã sửa từ `gemini-pro` → `gemini-1.5-flash`
2. **Thiếu environment variables**: Vercel không đọc file .env

## 🔧 Cách khắc phục

### Bước 1: Cấu hình Environment Variables trên Vercel
1. Vào **Vercel Dashboard** → Chọn project
2. Vào **Settings** → **Environment Variables**
3. Thêm biến môi trường:
   ```
   Name: VITE_GEMINI_API_KEY
   Value: AIzaSyCBdYSpbgdP8NGBxZdsexliuBlcoyOJiYE
   ```
4. Chọn **All Environments** (Production, Preview, Development)
5. Click **Save**

### Bước 2: Redeploy
1. Vào **Deployments** tab
2. Click **Redeploy** trên deployment mới nhất
3. Hoặc push code mới để trigger auto-deploy

### Bước 3: Kiểm tra
1. Mở website trên Vercel
2. Test AI chat để xem có hoạt động không
3. Kiểm tra Console (F12) xem còn lỗi gì không

## 🔍 Debug
Nếu vẫn lỗi, kiểm tra:
1. **API Key có đúng không**: Phải bắt đầu bằng `AIza...`
2. **API Key có quyền truy cập Gemini không**
3. **Vùng địa lý có được hỗ trợ không**

## 📝 Lưu ý
- File .env chỉ hoạt động ở local development
- Vercel cần cấu hình Environment Variables riêng
- Sau khi thay đổi env vars phải redeploy
