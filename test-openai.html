<!DOCTYPE html>
<html>
<head>
    <title>Test OpenAI API</title>
</head>
<body>
    <h1>Test OpenAI API</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script type="module">
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing OpenAI API...';
            
            try {
                const apiKey = '********************************************************************************************************************************************************************';
                
                // Import OpenAI
                const { OpenAI } = await import('https://esm.run/openai@4.20.1');
                
                const openai = new OpenAI({
                    apiKey: apiKey,
                    dangerouslyAllowBrowser: true
                });
                
                resultDiv.innerHTML += '<br>Testing GPT-4o-mini...';
                
                const completion = await openai.chat.completions.create({
                    model: "gpt-4o-mini",
                    messages: [
                        {
                            role: "system",
                            content: "Bạn là AI chuyên gia về tư tưởng Hồ Chí Minh."
                        },
                        {
                            role: "user", 
                            content: "Tư tưởng độc lập dân tộc của Hồ Chí Minh có đặc điểm gì?"
                        }
                    ],
                    max_tokens: 200
                });
                
                const response = completion.choices[0]?.message?.content;
                resultDiv.innerHTML += `<br>✅ OpenAI works! Response: ${response?.substring(0, 200)}...`;
                
            } catch (error) {
                resultDiv.innerHTML += `<br>❌ Error: ${error.message}`;
            }
        }
        
        window.testAPI = testAPI;
    </script>
</body>
</html>
