<!DOCTYPE html>
<html>
<head>
    <title>Test Gemini API</title>
</head>
<body>
    <h1>Test Gemini API</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script type="module">
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const apiKey = 'AIzaSyCBdYSpbgdP8NGBxZdsexliuBlcoyOJiYE';
                
                // Import Google Generative AI
                const { GoogleGenerativeAI } = await import('https://esm.run/@google/generative-ai');
                
                const genAI = new GoogleGenerativeAI(apiKey);
                
                // Test different models
                const models = [
                    'gemini-1.5-flash',
                    'gemini-1.5-pro',
                    'gemini-pro',
                    'gemini-1.0-pro'
                ];
                
                for (const modelName of models) {
                    try {
                        resultDiv.innerHTML += `<br>Testing ${modelName}...`;
                        
                        const model = genAI.getGenerativeModel({ model: modelName });
                        const result = await model.generateContent('Hello, test message');
                        const response = await result.response;
                        const text = response.text();
                        
                        resultDiv.innerHTML += `<br>✅ ${modelName} works! Response: ${text.substring(0, 100)}...`;
                        break;
                        
                    } catch (error) {
                        resultDiv.innerHTML += `<br>❌ ${modelName} failed: ${error.message}`;
                    }
                }
                
            } catch (error) {
                resultDiv.innerHTML += `<br>❌ General error: ${error.message}`;
            }
        }
        
        window.testAPI = testAPI;
    </script>
</body>
</html>
